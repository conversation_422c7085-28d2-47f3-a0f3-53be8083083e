.\" Man page generated from reStructuredText.
.
.TH MOGGSPLIT 1 "" "" ""
.SH NAME
moggsplit \- split Ogg logical streams
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
\fBmoggsplit\fP \fIfilename\fP ...
.SH DESCRIPTION
.sp
\fBmoggsplit\fP splits a multiplexed Ogg stream into separate files. For
example, it can separate an OGM into separate Ogg DivX and Ogg Vorbis
streams, or a chained Ogg Vorbis file into two separate files.
.SH OPTIONS
.INDENT 0.0
.TP
.B \-\-extension
Use the supplied extension when generating new files; the default is
\fBogg\fP\&.
.TP
.B \-\-pattern
Use the supplied pattern when generating new files. This is a Python
keyword format string with three variables, \fIbase\fP for the original
file\(aqs base name, \fIstream\fP for the stream\(aqs serial number, and ext for
the extension give by \fB\-\-extension\fP\&.
.sp
The default is \fB%(base)s\-%(stream)d.%(ext)s\fP\&.
.TP
.B \-\-m3u
Generate an m3u playlist along with the newly generated files. Useful
for large chained Oggs.
.UNINDENT
.SH AUTHOR
.sp
Joe Wreschnig
.\" Generated by docutils manpage writer.
.
