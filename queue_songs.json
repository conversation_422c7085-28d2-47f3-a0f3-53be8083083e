[{"artist": "<PERSON>", "song_name": "Black Rover", "link": "https://open.spotify.com/track/0dnkpPwcHK8UIZXhhN86Ss"}, {"artist": "<PERSON>", "song_name": "Bloody Stream", "link": "https://open.spotify.com/track/3NM0fXiFnWiCVBxSYRkKpL"}, {"artist": "<PERSON>", "song_name": "Harumachi Clover", "link": "https://open.spotify.com/track/24CYIJMuIwaDaRzlPoxqKK"}, {"artist": "<PERSON>", "song_name": "RESISTER", "link": "https://open.spotify.com/track/5lpjxpIMt4T5x8nXIdpFTo"}, {"artist": "<PERSON>", "song_name": "<PERSON> Catcher", "link": "https://open.spotify.com/track/2dGKZX6sWPOoQ6XGpUPtMA"}, {"artist": "<PERSON>", "song_name": "Galactic Mermaid", "link": "https://open.spotify.com/track/24qwEWTL33JdZSR9I1L1dy"}, {"artist": "<PERSON>", "song_name": "<PERSON><PERSON><PERSON>", "link": "https://open.spotify.com/track/4EDYtZpnSYZnKvHfLGkd3z"}, {"artist": "<PERSON>", "song_name": "<PERSON><PERSON><PERSON>", "link": "https://open.spotify.com/track/0QSq4VBMFxcish1tak06PE"}, {"artist": "<PERSON>", "song_name": "<PERSON><PERSON> no <PERSON>", "link": "https://open.spotify.com/track/38vvNHDjXFzApK1EejnTwp"}, {"artist": "<PERSON>", "song_name": "Of Our Time", "link": "https://open.spotify.com/track/1aN9YKXIBeTLmuhyskC22C"}, {"artist": "<PERSON>", "song_name": "Of Our Time - osu! Edit", "link": "https://open.spotify.com/track/0paIfRT6KwnpKeZUqL4UqH"}, {"artist": "<PERSON>", "song_name": "Of Our Time - Instrumental", "link": "https://open.spotify.com/track/3hoCUTvebZQrsIBIknzCXN"}, {"artist": "<PERSON>", "song_name": "BIRDBRAIN", "link": "https://open.spotify.com/track/1OtMWjz5ww69gGQp4cW1HQ"}, {"artist": "<PERSON>", "song_name": "Imitation", "link": "https://open.spotify.com/track/46UEHk52GJ2O53meiZURDF"}, {"artist": "<PERSON>", "song_name": "A Better Tomorrow", "link": "https://open.spotify.com/track/4vgqhv1vdaOdJsLLj6dZVP"}, {"artist": "<PERSON>", "song_name": "MoeChakkaFire", "link": "https://open.spotify.com/track/1X2PRFsFO5eKS1DBuwEf7z"}, {"artist": "<PERSON>", "song_name": "Going, Going, Gone", "link": "https://open.spotify.com/track/6RQrq7QzeNp8ogThm5IuYa"}, {"artist": "<PERSON>", "song_name": "Going, Going, Gone - Nightcore Ver.", "link": "https://open.spotify.com/track/3mXuDcZtjHPvDFaNqUxp02"}, {"artist": "<PERSON>", "song_name": "Rabbit Hole", "link": "https://open.spotify.com/track/14XEG5eCBFYAHaY69PxJ0q"}, {"artist": "<PERSON>", "song_name": "<PERSON>", "link": "https://open.spotify.com/track/4v2ODjckvGck1PKeqPafDC"}, {"artist": "<PERSON>", "song_name": "<PERSON><PERSON>", "link": "https://open.spotify.com/track/68w5VqDsCnhg3ZJ8VhIFbB"}, {"artist": "<PERSON>", "song_name": "Mo<PERSON><PERSON> Lesson", "link": "https://open.spotify.com/track/3GeeqNhbIL9fLyBU3ATBRo"}, {"artist": "<PERSON>", "song_name": "My R", "link": "https://open.spotify.com/track/2bKH0plhNdw9l2Br517YeW"}, {"artist": "<PERSON>", "song_name": "Through the Dark", "link": "https://open.spotify.com/track/6p0kWG5dCFc1HdZKkxaZM8"}, {"artist": "<PERSON>", "song_name": "Writing on the Wall", "link": "https://open.spotify.com/track/5rxbkKhHOLYTRe066svNMI"}, {"artist": "<PERSON>", "song_name": "Writing on the Wall - Nightcore Ver.", "link": "https://open.spotify.com/track/031IArpqjDgQy35shEuD5C"}, {"artist": "<PERSON>", "song_name": "Koala", "link": "https://open.spotify.com/track/64k7ScemGeUI3buDgiThmU"}, {"artist": "<PERSON>", "song_name": "Koala - Self Cover", "link": "https://open.spotify.com/track/1qQLSsnXT0eeNC4kJVk6sK"}, {"artist": "<PERSON>", "song_name": "Sacabambapis", "link": "https://open.spotify.com/track/5xohP9ZvU6z8NxTgrECTfU"}, {"artist": "<PERSON>", "song_name": "golden hour", "link": "https://open.spotify.com/track/2QVHiTYiB465xc1ExaingE"}, {"artist": "<PERSON>", "song_name": "Signore dei Lupi - from Arknights", "link": "https://open.spotify.com/track/1G6UIF2o22xRjg99slEhn5"}, {"artist": "<PERSON>", "song_name": "Live in Life", "link": "https://open.spotify.com/track/0Ghyy5KfnsLDMfz5qXKHyz"}, {"artist": "<PERSON>", "song_name": "NIGHT DANCER", "link": "https://open.spotify.com/track/2DEmhrkawDl6cF00EZBAQu"}, {"artist": "<PERSON>", "song_name": "<PERSON><PERSON><PERSON>", "link": "https://open.spotify.com/track/2RCm1IwE4BLepntdty5Oy9"}, {"artist": "<PERSON>", "song_name": "Kawaikutegomen", "link": "https://open.spotify.com/track/4HgCwqDkrWdl8KwglZfUlm"}, {"artist": "<PERSON>", "song_name": "Shinunoga E-Wa", "link": "https://open.spotify.com/track/6abv1McrGPQRS7KOAzmWDe"}, {"artist": "<PERSON>", "song_name": "Cute Na Kanojo", "link": "https://open.spotify.com/track/35yKlhuxhaDEJvpzeBNVZ4"}, {"artist": "<PERSON>", "song_name": "<PERSON><PERSON><PERSON>", "link": "https://open.spotify.com/track/04jY1JR9iC8X10M9vT0brP"}, {"artist": "<PERSON>", "song_name": "New Genesis", "link": "https://open.spotify.com/track/1JxDuafdqnRHA4PWbI1ksv"}, {"artist": "<PERSON>", "song_name": "Telecaster B Boy", "link": "https://open.spotify.com/track/5ti2e1ftgNCNk9dtxIEWDe"}, {"artist": "<PERSON>", "song_name": "QUEEN", "link": "https://open.spotify.com/track/10wliawFpBlm75T8JMVOVD"}, {"artist": "<PERSON>", "song_name": "Comedy", "link": "https://open.spotify.com/track/6V5MLoxnkO0DcCj2PawmBf"}, {"artist": "<PERSON>", "song_name": "God-ish", "link": "https://open.spotify.com/track/2fuXk4dsa3aeVdIsTohiy8"}, {"artist": "<PERSON>", "song_name": "Lower One's Eyes", "link": "https://open.spotify.com/track/7sY5E3YFUyNSibSoIE9Z2P"}, {"artist": "<PERSON>", "song_name": "Lagtrain", "link": "https://open.spotify.com/track/3Mrd6U5HDnM8LuzCM3emJE"}, {"artist": "<PERSON>", "song_name": "Hello Marina", "link": "https://open.spotify.com/track/4aGlqr5GI9RqcunVL567cv"}, {"artist": "<PERSON>", "song_name": "<PERSON><PERSON>", "link": "https://open.spotify.com/track/6bytutF1v0Sd57LVlqyzPT"}, {"artist": "<PERSON>", "song_name": "Lost Umbrella - yuigot mix", "link": "https://open.spotify.com/track/6Rs8WWygzpMvLHibZmLSKC"}, {"artist": "<PERSON>", "song_name": "Mixed Nuts", "link": "https://open.spotify.com/track/6LU2z3Tc3aRbqR2sgJcu3b"}, {"artist": "<PERSON>", "song_name": "phony", "link": "https://open.spotify.com/track/30vb8bp4pUodvRohGBtOeQ"}, {"artist": "<PERSON>", "song_name": "Identity", "link": "https://open.spotify.com/track/2fQITZdIcuWEhR76QqSPWz"}, {"artist": "<PERSON>", "song_name": "Brain Fluid Explosion Girl", "link": "https://open.spotify.com/track/172TDcE8H2zT2vxtoHeoks"}, {"artist": "<PERSON>", "song_name": "Snow Halation", "link": "https://open.spotify.com/track/2N76IteNq2CHWtWAW80afE"}, {"artist": "<PERSON>", "song_name": "KING", "link": "https://open.spotify.com/track/4MRUADEUrhh6cbg6SvQIIs"}, {"artist": "<PERSON>", "song_name": "Goodbye Sengen", "link": "https://open.spotify.com/track/6rsXDS9vzAFkCllFxHCDLG"}, {"artist": "<PERSON>", "song_name": "Cry Baby", "link": "https://open.spotify.com/track/3GXbQStCJPbRiqrpXH7hix"}, {"artist": "<PERSON>", "song_name": "USSEEWA", "link": "https://open.spotify.com/track/5fB2T3PDQRCA6aalHkR1DI"}, {"artist": "<PERSON>", "song_name": "Castaways", "link": "https://open.spotify.com/track/4qk4cdKtIE2YGBLUcMDpNi"}, {"artist": "<PERSON>", "song_name": "Comet", "link": "https://open.spotify.com/track/2vceIbXdGcshaYVv4XXZD8"}, {"artist": "<PERSON>", "song_name": "Ghost In A Flower", "link": "https://open.spotify.com/track/5sA9kG9kvPx5y98RohKr7n"}, {"artist": "<PERSON>", "song_name": "Hated by life itself.", "link": "https://open.spotify.com/track/40QlL6BeQmy5v6pHeyU2JN"}, {"artist": "<PERSON>", "song_name": "It's Beginning to Look a Lot Like Christmas", "link": "https://open.spotify.com/track/1aGbCs0gG5CtXTPM1Ex1un"}, {"artist": "<PERSON>", "song_name": "Winter Wonderland", "link": "https://open.spotify.com/track/6xXtWTZAwPqXXgdYcysFFP"}, {"artist": "<PERSON>", "song_name": "Santa Claus Is Comin' to Town", "link": "https://open.spotify.com/track/2IhM9uTigWMdudRQ2Up2FR"}, {"artist": "<PERSON>", "song_name": "Have Yourself a Merry Little Christmas", "link": "https://open.spotify.com/track/5MUTrOyRr8aNpQoGO38g0y"}, {"artist": "<PERSON><PERSON>", "song_name": "タイド", "link": "https://open.spotify.com/track/6f0qFRre5X5M4YtdJ5G4JW"}, {"artist": "<PERSON><PERSON>", "song_name": "SNEAKING - revenge", "link": "https://open.spotify.com/track/1v6f14x9mByNuGwlLUjAfk"}, {"artist": "<PERSON><PERSON>", "song_name": "Go-Getters", "link": "https://open.spotify.com/track/2yW9IPUsCxoUlxjAguGNa4"}, {"artist": "<PERSON><PERSON>", "song_name": "Overkill", "link": "https://open.spotify.com/track/3E5yndl8ohgFGJTwtCsZrQ"}, {"artist": "<PERSON><PERSON>", "song_name": "Cult Following", "link": "https://open.spotify.com/track/4Yh33flAK4zjKc9eeh4klW"}, {"artist": "<PERSON><PERSON>", "song_name": "DONMAI", "link": "https://open.spotify.com/track/3MtScqe0Jkt6aBz6l3m0re"}, {"artist": "<PERSON><PERSON>", "song_name": "<PERSON>", "link": "https://open.spotify.com/track/2xE8s1cWml9Hgmw3tBaj6h"}, {"artist": "<PERSON><PERSON>", "song_name": "skeletons", "link": "https://open.spotify.com/track/2b4OGV2zPRqboRMdnHsaEK"}, {"artist": "<PERSON><PERSON>", "song_name": "Last Days", "link": "https://open.spotify.com/track/462pBu9HGyPFxt6v1Nurtm"}, {"artist": "<PERSON><PERSON>", "song_name": "through the blue", "link": "https://open.spotify.com/track/2QC9k5BwDKyOGMwBUnqyOZ"}, {"artist": "<PERSON><PERSON>", "song_name": "Go-Getters", "link": "https://open.spotify.com/track/68p6vHlTKM5NtR2PwkA9Mt"}, {"artist": "<PERSON><PERSON>", "song_name": "Taste of Death", "link": "https://open.spotify.com/track/5SjhgsZnYxlW196A4mhyY9"}, {"artist": "<PERSON><PERSON>", "song_name": "Wanted, Wasted", "link": "https://open.spotify.com/track/7aDrJbO2oxuEKG9HMehSON"}, {"artist": "<PERSON><PERSON>", "song_name": "I’m Greedy", "link": "https://open.spotify.com/track/0JrUTFIcxNB6qs2FgSYiS9"}, {"artist": "<PERSON><PERSON>", "song_name": "Internet Brain Rot", "link": "https://open.spotify.com/track/1PcJs1tEcLajTlL8SEAjos"}, {"artist": "<PERSON><PERSON>", "song_name": "NEZUMI Scheme", "link": "https://open.spotify.com/track/0wcbWLz1kTgL0kb2y6Rh3U"}, {"artist": "<PERSON><PERSON>", "song_name": "soul food", "link": "https://open.spotify.com/track/4ijPgRq7mi8WQNtH3W8ZlD"}, {"artist": "<PERSON><PERSON>", "song_name": "CRINGECORE", "link": "https://open.spotify.com/track/0usGbh8Z3eIu04sOKjqO9C"}, {"artist": "<PERSON><PERSON>", "song_name": "Dance Past Midnight", "link": "https://open.spotify.com/track/6bI6To4KlW4oqQiUwoBN0Z"}, {"artist": "<PERSON><PERSON>", "song_name": "Death Sentence", "link": "https://open.spotify.com/track/5I507ZS0IN8SUK8NZRUszh"}, {"artist": "<PERSON><PERSON>", "song_name": "glass slipper", "link": "https://open.spotify.com/track/0BTFMFqeybee11L7hO7Hi6"}, {"artist": "<PERSON><PERSON>", "song_name": "UnAlive", "link": "https://open.spotify.com/track/0U0swpLFeIJV1bP9QjSkyz"}, {"artist": "<PERSON><PERSON>", "song_name": "Q", "link": "https://open.spotify.com/track/3iFh4XNSePkLOUN0McOQvi"}, {"artist": "<PERSON><PERSON>", "song_name": "Dead On Arrival", "link": "https://open.spotify.com/track/4Ri7HgYsTqCoNqHI2S7wVB"}, {"artist": "<PERSON><PERSON>", "song_name": "Graveyard Shift ft. BOOGEY VOXX", "link": "https://open.spotify.com/track/5fmT83NSF38oaQelfJl4Lx"}, {"artist": "<PERSON><PERSON>", "song_name": "Lose-Lose Days", "link": "https://open.spotify.com/track/6BX000G0i1VJcff38x3grU"}, {"artist": "<PERSON><PERSON>", "song_name": "HUGE W", "link": "https://open.spotify.com/track/3v28Meo8cBnnfDUzpF6Art"}, {"artist": "<PERSON><PERSON>", "song_name": "Resting Power", "link": "https://open.spotify.com/track/6pMunEPmwPTffz5qO0NOkJ"}, {"artist": "<PERSON><PERSON>", "song_name": "Scuffed Up Age", "link": "https://open.spotify.com/track/6at1OnNumlOOQMc6Nb1sbU"}, {"artist": "<PERSON><PERSON>", "song_name": "Ouroboros", "link": "https://open.spotify.com/track/5Ak9haMI6MkAB9qAGZV9jq"}, {"artist": "<PERSON><PERSON>", "song_name": "UnAlive - Japanese Version", "link": "https://open.spotify.com/track/2EOG8ynjNUIq7WC2L3KgTC"}, {"artist": "<PERSON><PERSON>", "song_name": "UnAlive - Instrumental", "link": "https://open.spotify.com/track/7M49f6eJPX2XQWqKv0ocL1"}, {"artist": "<PERSON><PERSON>", "song_name": "The Grim Reaper is a Live-Streamer - Lofi Ver.", "link": "https://open.spotify.com/track/5g5q3kDortKz2eCOAUrCLw"}, {"artist": "<PERSON><PERSON>", "song_name": "Red - <PERSON><PERSON>.", "link": "https://open.spotify.com/track/6TNs0DLoQD7xzKVDI0oD4i"}, {"artist": "<PERSON><PERSON>", "song_name": "guh - <PERSON><PERSON>.", "link": "https://open.spotify.com/track/0XNGcTUlJNdNyd2bWX8RYF"}, {"artist": "<PERSON><PERSON>", "song_name": "いじめっ子Bully - <PERSON><PERSON>.", "link": "https://open.spotify.com/track/6aV3CxXoFeXECMTYDjqUAQ"}, {"artist": "<PERSON><PERSON>", "song_name": "The Grim Reaper is a Live-Streamer - Lofi Ver. Instrumental", "link": "https://open.spotify.com/track/0pIQRoSrjVeXMsBV9datMI"}, {"artist": "<PERSON><PERSON>", "song_name": "Red - Lo<PERSON>. Instrumental", "link": "https://open.spotify.com/track/5kR83AlW4I7rapeKwirMwQ"}, {"artist": "<PERSON><PERSON>", "song_name": "guh - <PERSON><PERSON> Ver. Instrumental", "link": "https://open.spotify.com/track/0fYOcqEGWnZs5UPBcZMxPx"}, {"artist": "<PERSON><PERSON>", "song_name": "いじめっ子Bully - <PERSON><PERSON>. Instrumental", "link": "https://open.spotify.com/track/679qfnQOIjUN7RHGZyhyWl"}, {"artist": "<PERSON><PERSON>", "song_name": "<PERSON><PERSON><PERSON>", "link": "https://open.spotify.com/track/5tROXAnQQpTJNkC44M2F8c"}, {"artist": "<PERSON><PERSON>", "song_name": "Die For You", "link": "https://open.spotify.com/track/6cLd2xZirorZbZY5500XDA"}, {"artist": "<PERSON><PERSON>", "song_name": "Sepia", "link": "https://open.spotify.com/track/2p4abroaLYk6rXgm598mmq"}, {"artist": "<PERSON><PERSON>", "song_name": "Seeing Stars", "link": "https://open.spotify.com/track/3ZKKYwnkAnnLRT0WarwJzb"}, {"artist": "<PERSON><PERSON>", "song_name": "Go-Getters", "link": "https://open.spotify.com/track/0AjPUYIhuvGWrPWT9T02JH"}, {"artist": "<PERSON><PERSON>", "song_name": "<PERSON>", "link": "https://open.spotify.com/track/7f7v2N5STXFIm2X7W0K5Cd"}, {"artist": "<PERSON><PERSON>", "song_name": "タイド", "link": "https://open.spotify.com/track/4PnRDSHBDoG7WUS8exZ3yl"}, {"artist": "<PERSON><PERSON>", "song_name": "Overkill", "link": "https://open.spotify.com/track/1UlQyJezbjrwRlC2fo3D3i"}, {"artist": "<PERSON><PERSON>", "song_name": "SNEAKING", "link": "https://open.spotify.com/track/4zYlju4viLB237Htiz3zgo"}, {"artist": "<PERSON><PERSON>", "song_name": "虚像のCarousel", "link": "https://open.spotify.com/track/1w3bvVM9uo7Q5JWb2EV84f"}, {"artist": "<PERSON><PERSON>", "song_name": "未来島", "link": "https://open.spotify.com/track/7Icw3Fe3tsJJ2LkQVaAUEK"}, {"artist": "<PERSON><PERSON>", "song_name": "Black Sheep", "link": "https://open.spotify.com/track/76fIkBJQtCdBFcfyNpWPWD"}, {"artist": "<PERSON><PERSON>", "song_name": "six feet under", "link": "https://open.spotify.com/track/3QryCjbtTc1KR5LPcbm61r"}, {"artist": "<PERSON><PERSON>", "song_name": "You're Not Special", "link": "https://open.spotify.com/track/2assykTcw0YvTyZD7XSGMd"}, {"artist": "<PERSON><PERSON>", "song_name": "Left For Dead Lullaby", "link": "https://open.spotify.com/track/5VWVXxVIcv3z2ORnwQbvke"}, {"artist": "<PERSON><PERSON>", "song_name": "虚像のCarousel", "link": "https://open.spotify.com/track/2FCkgW3vOc3IvpOCd0LJMT"}, {"artist": "<PERSON><PERSON>", "song_name": "six feet under", "link": "https://open.spotify.com/track/1LNsuwA5ql2sUcbSgjX42q"}, {"artist": "<PERSON><PERSON>", "song_name": "未来島", "link": "https://open.spotify.com/track/0QZXP7ZS064QBr1ZgHUblt"}, {"artist": "<PERSON><PERSON>", "song_name": "NEZUMI Scheme", "link": "https://open.spotify.com/track/7IWwWWak1Ebp7ni7PTfhMx"}, {"artist": "<PERSON><PERSON>", "song_name": "I’m Greedy", "link": "https://open.spotify.com/track/0fAsMU3MwXLoXzXsyByqKh"}, {"artist": "<PERSON><PERSON>", "song_name": "CapSule", "link": "https://open.spotify.com/track/55McbqAUspPoavubDJxryF"}, {"artist": "<PERSON><PERSON>", "song_name": "MERA MERA", "link": "https://open.spotify.com/track/3hOTaPhf3gLUZhqZgoUzme"}, {"artist": "<PERSON><PERSON>", "song_name": "<PERSON><PERSON><PERSON><PERSON>", "link": "https://open.spotify.com/track/5xpjYTlkYg8o92LMOvG3eV"}, {"artist": "<PERSON><PERSON>", "song_name": "<PERSON> <PERSON><PERSON>id", "link": "https://open.spotify.com/track/6ADAhVnCCdv06G30KbLO1C"}, {"artist": "<PERSON><PERSON>", "song_name": "Holy嫉妬", "link": "https://open.spotify.com/track/4KtsVWPHvVTLBwU7rtTBwn"}, {"artist": "<PERSON><PERSON>", "song_name": "Let’s End the World", "link": "https://open.spotify.com/track/7hT7tA7VUkkNihDKFt1fuA"}, {"artist": "<PERSON><PERSON>", "song_name": "Holy嫉妬", "link": "https://open.spotify.com/track/0hclaftCnRygqy5cFubW4f"}, {"artist": "<PERSON><PERSON>", "song_name": "MERA MERA", "link": "https://open.spotify.com/track/3Y5q0JDCRzjNlMRRrLK0RF"}, {"artist": "<PERSON><PERSON>", "song_name": "CapSule", "link": "https://open.spotify.com/track/4M7mmhZvhN1wBypbu0Z0zY"}, {"artist": "<PERSON><PERSON>", "song_name": "Q", "link": "https://open.spotify.com/track/51CXAV2GNNL3deCtcXpCeu"}, {"artist": "<PERSON><PERSON>", "song_name": "<PERSON> Blue", "link": "https://open.spotify.com/track/4hKvx9VUiH4sDJcNLscuTO"}, {"artist": "<PERSON><PERSON>", "song_name": "<PERSON> - Instrumental", "link": "https://open.spotify.com/track/0F0PAyVZ9nc2ZIq5ycVsq3"}, {"artist": "<PERSON><PERSON>", "song_name": "Graveyard Shift ft. BOOGEY VOXX", "link": "https://open.spotify.com/track/6KhX2ctFvidBjIUPrKaGqs"}, {"artist": "<PERSON><PERSON>", "song_name": "end of a life", "link": "https://open.spotify.com/track/0i2THDeAhJma8FrUVy90No"}, {"artist": "<PERSON><PERSON>", "song_name": "end of a life（Instrumental）", "link": "https://open.spotify.com/track/4LZhqIqJb6eQhYXYIqUysb"}, {"artist": "<PERSON><PERSON>", "song_name": "The Grim Reaper is a Live-Streamer", "link": "https://open.spotify.com/track/6i3It0rVKEvoaTkzIxGOpg"}, {"artist": "<PERSON><PERSON>", "song_name": "Red", "link": "https://open.spotify.com/track/3h8efEQ3MsDESpfeKX1JI4"}, {"artist": "<PERSON><PERSON>", "song_name": "guh", "link": "https://open.spotify.com/track/1rae8blMgdy6WQgZAHxRXN"}, {"artist": "<PERSON><PERSON>", "song_name": "いじめっ子Bully", "link": "https://open.spotify.com/track/1Mt287tRgGUKQtB885KHhF"}, {"artist": "<PERSON><PERSON>", "song_name": "居場所", "link": "https://open.spotify.com/track/2bQU3PVpYEpos1J2vYKMdT"}, {"artist": "<PERSON><PERSON>", "song_name": "Off With Their Heads", "link": "https://open.spotify.com/track/1ffXf7HjLCHvNZngkQpGTR"}, {"artist": "<PERSON><PERSON>", "song_name": "Cursed Night", "link": "https://open.spotify.com/track/5w2OzFwcgTjQ5bhk50wnkp"}, {"artist": "<PERSON><PERSON>", "song_name": "ReaperかRapper? 自己紹介ラップ", "link": "https://open.spotify.com/track/5hv7Kq6CGGI9tkoT8yZZKs"}, {"artist": "<PERSON><PERSON>", "song_name": "失礼しますが、RIP♡", "link": "https://open.spotify.com/track/4luSvNPtu6emYsaTOHrguR"}, {"artist": "<PERSON><PERSON>", "song_name": "DEAD BEATS", "link": "https://open.spotify.com/track/3ZkYgSk8cIqOlvBlK7IV6C"}, {"artist": "<PERSON><PERSON>", "song_name": "Live Again", "link": "https://open.spotify.com/track/4Xnuh5fEHbHpxI3leaQSLN"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "You can love me all the time. - <PERSON>.", "link": "https://open.spotify.com/track/3w4PhaE676rIuoxNlQG5Mx"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "Like I'll be a bum for the rest of my life. - <PERSON>.", "link": "https://open.spotify.com/track/1KHsvJLqdsWAd3P7zUyt85"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "I don't know why I live here. - <PERSON>.", "link": "https://open.spotify.com/track/51EHJ0BNHIafma8s229lWj"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "just saying, the body is honest. - <PERSON>.", "link": "https://open.spotify.com/track/08LOnSNbtjvEPYAvfanVSX"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "If you are happy, I will burn and die. - <PERSON>.", "link": "https://open.spotify.com/track/1Jj6zZDoIFIn17aXx5WtvP"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "I want to stay until morning with you today. - <PERSON>.", "link": "https://open.spotify.com/track/58pCFR1sXKUY4Sz8LAcLGb"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "<PERSON> Mel<PERSON> - <PERSON> Ver.", "link": "https://open.spotify.com/track/2uAAPP0hbe4Sl0VLzkPirO"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "Cinderella Master Plan - <PERSON> Ver.", "link": "https://open.spotify.com/track/7qLX4KSoBYDKOwfsY5nIxI"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "Only Love Eternal - <PERSON>er.", "link": "https://open.spotify.com/track/3LQmYB3YkRLIW55IcjkvrK"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "amalgam", "link": "https://open.spotify.com/track/00cZaF58Gdz0TjznhAIW7g"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "lemonade", "link": "https://open.spotify.com/track/4MCu8F9Cc1myihnuhegUsH"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "in muddy water", "link": "https://open.spotify.com/track/5GVr62daHxJ22CrGQ7SQIB"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "Limbs leaking from the umbrella", "link": "https://open.spotify.com/track/5xk6VAHANiehzGoqNYl25J"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "I want to be poisoned by you and drown", "link": "https://open.spotify.com/track/1EXISH6kISEMaqXAoD8uEO"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "you were wet", "link": "https://open.spotify.com/track/3zO1JpMLOlrHzASQhvA29G"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "crumb", "link": "https://open.spotify.com/track/2MrQ1nPTaIzvkSqZi4Tpfc"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "laundry", "link": "https://open.spotify.com/track/2YXuWi4A1RlVLTFioL7aXk"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "patchwork", "link": "https://open.spotify.com/track/5Dbdcag3QDbnCokE3sv1rD"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "babe", "link": "https://open.spotify.com/track/6veW6hZauPXbfwt7nzE12K"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "A Soaked Heart", "link": "https://open.spotify.com/track/6ngqldhZ3URRUt3JOccyb2"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "just saying, the body is honest.", "link": "https://open.spotify.com/track/5lp2Y0gI945doCZ2ZgYjEz"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "I'm falling in love with my teacher", "link": "https://open.spotify.com/track/5onYxUOsUKZEgTzM242byp"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "life is not easy", "link": "https://open.spotify.com/track/7zRRXZe9RYU6LRhyXfP8v6"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "just saying, the body is honest. - self cover", "link": "https://open.spotify.com/track/1dLfVxNVYocbLKt3Abrj9j"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "I'm falling in love with my teacher - self cover", "link": "https://open.spotify.com/track/2UZXvghxBiTRnUSaFoGHun"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "life is not easy - self cover", "link": "https://open.spotify.com/track/2CwYkQn6K8w3NzK5HMz393"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "i know you like me", "link": "https://open.spotify.com/track/7vzfiBmJ7KczqdKdUxgXrS"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "LOVE is Scrap", "link": "https://open.spotify.com/track/0xfXeV833DDzaHpelWpV8b"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "Paralyze Girl", "link": "https://open.spotify.com/track/1hu9YrPHlgxr3O0iBT1A24"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "plastic for smile", "link": "https://open.spotify.com/track/6BqoONRTbMzrwxwGzezhaY"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "just saying, the body is honest. - self cover", "link": "https://open.spotify.com/track/2DgdKi8giLlUr3HhVfYsdY"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "life is not easy", "link": "https://open.spotify.com/track/04bFfVo6adw40eWae09K9j"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "I'm falling in love with my teacher", "link": "https://open.spotify.com/track/3ZwXWF0AEfDhSH8uj24OIJ"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "god's parasol - <PERSON> Ver.", "link": "https://open.spotify.com/track/3HPoOIuE76PzXxKv6Lbe2N"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "I'm falling in love with my teacher - <PERSON>.", "link": "https://open.spotify.com/track/2wMC0tRvRzeIMOd17bONlG"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "Farewell After Bloom - <PERSON>.", "link": "https://open.spotify.com/track/6s7Q0or22HlNd4bn8KWXC6"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "Give love to the brain of the void - <PERSON>.", "link": "https://open.spotify.com/track/6DbQWNTLd70QDObqz1Jqw7"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "your cheeks turned pink - <PERSON> Ver.", "link": "https://open.spotify.com/track/0fG4wq8JRXxZchYXEvQ1p9"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "Even a love that has ended - <PERSON>.", "link": "https://open.spotify.com/track/0E218auy8Aoc6MHn89GCe7"}, {"artist": "s<PERSON><PERSON><PERSON> sougo", "song_name": "just saying, the body is honest.", "link": "https://open.spotify.com/track/4z1O8W35JfmFjPlD9KYvid"}]