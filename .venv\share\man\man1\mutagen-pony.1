.\" Man page generated from reStructuredText.
.
.TH MUTAGEN-PONY 1 "" "" ""
.SH NAME
mutagen-pony \- scan a collection of MP3 files
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
\fBmutagen\-pony\fP \fIdirectory\fP ...
.SH DESCRIPTION
.sp
\fBmutagen\-pony\fP scans any directories given and reports on the kinds of
tags in the MP3s it finds in them. Ride the pony.
.sp
It is primarily intended as a debugging tool for Mutagen.
.SH AUTHORS
.sp
Michael Urman and Joe Wreschnig
.\" Generated by docutils manpage writer.
.
