.\" Man page generated from reStructuredText.
.
.TH MID3CP 1 "" "" ""
.SH NAME
mid3cp \- copy ID3 tags
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
\fBmid3cp\fP [\fIoptions\fP] \fIsource\fP \fIdest\fP
.SH DESCRIPTION
.sp
\fBmid3cp\fP copies the ID3 tags from a source file to a destination file.
.sp
It is designed to provide similar functionality to id3lib\(aqs id3cp tool, and can
optionally write ID3v1 tags. It can also exclude specific tags from being
copied.
.SH OPTIONS
.INDENT 0.0
.TP
.B \-\-verbose\fP,\fB  \-v
Be verbose: state all operations performed, and list tags in source file.
.TP
.B \-\-write\-v1
Write ID3v1 tags to the destination file, derived from the ID3v2 tags.
.TP
.B \-\-exclude\-tag\fP,\fB  \-x
Exclude a specific tag from being copied. Can be specified multiple times.
.TP
.B \-\-merge
Copy over frames instead of replacing the whole ID3 tag. The tag version
of \fIdest\fP will be used. In case \fIdest\fP has no ID3 tag this option has no
effect.
.UNINDENT
.SH AUTHOR
.sp
Marcus Sundman.
.sp
Based on id3cp (part of id3lib) by Dirk Mahoney and Scott Thomas Haug.
.\" Generated by docutils manpage writer.
.
