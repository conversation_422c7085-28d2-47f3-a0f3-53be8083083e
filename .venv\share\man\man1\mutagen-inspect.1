.\" Man page generated from reStructuredText.
.
.TH MUTAGEN-INSPECT 1 "" "" ""
.SH NAME
mutagen-inspect \- view Mutagen-supported audio tags
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
\fBmutagen\-inspect\fP \fIfilename\fP ...
.SH DESCRIPTION
.sp
\fBmutagen\-inspect\fP loads and prints information about an audio file and
its tags.
.sp
It is primarily intended as a debugging tool for Mutagen, but can be useful
for extracting tags from the command line.
.SH AUTHOR
.sp
Joe Wreschnig
.\" Generated by docutils manpage writer.
.
