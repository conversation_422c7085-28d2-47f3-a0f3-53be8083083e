"""
Background Scheduler for Artist Releases Downloader
Handles periodic tasks like checking for new releases and automatic downloads
"""

import os
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import Cron<PERSON>rigger
import spotipy
from spotipy.oauth2 import SpotifyOAuth

from session_manager import SessionManager


class BackgroundTaskScheduler:
    """Manages background tasks for automatic checking and downloading"""
    
    def __init__(self, session_manager: SessionManager, spotify_oauth: SpotifyOAuth):
        self.session_manager = session_manager
        self.spotify_oauth = spotify_oauth
        self.scheduler = BackgroundScheduler()
        self.is_running = False
        
        # Create logs directory
        os.makedirs('logs', exist_ok=True)
        
    def start(self):
        """Start the background scheduler"""
        if not self.is_running:
            self.scheduler.start()
            self.is_running = True
            print("🚀 Background scheduler started")
            
            # Schedule startup check
            self.schedule_startup_checks()
    
    def stop(self):
        """Stop the background scheduler"""
        if self.is_running:
            self.scheduler.shutdown()
            self.is_running = False
            print("🛑 Background scheduler stopped")
    
    def schedule_startup_checks(self):
        """Schedule checks for users who have startup checking enabled"""
        try:
            active_users = self.session_manager.get_all_active_users()
            
            for user_info in active_users:
                user_id = user_info['user_id']
                settings = self.session_manager.load_user_settings(user_id)
                
                if settings.get('check_on_startup', True):
                    # Schedule a one-time check in 30 seconds (to allow app to fully start)
                    self.scheduler.add_job(
                        func=self.check_for_new_releases,
                        trigger='date',
                        run_date=datetime.now() + timedelta(seconds=30),
                        args=[user_id],
                        id=f'startup_check_{user_id}',
                        replace_existing=True
                    )
                    print(f"📅 Scheduled startup check for user {user_info['user_name']}")
                
        except Exception as e:
            print(f"❌ Error scheduling startup checks: {e}")
    
    def schedule_periodic_checks(self, user_id: str):
        """Schedule periodic checks for a user based on their settings"""
        try:
            settings = self.session_manager.load_user_settings(user_id)
            
            if not settings.get('auto_check_enabled', False):
                # Remove existing job if auto-check is disabled
                self.remove_user_periodic_job(user_id)
                return
            
            interval_hours = settings.get('auto_check_interval_hours', 6)
            
            # Remove existing job first
            self.remove_user_periodic_job(user_id)
            
            # Add new periodic job
            self.scheduler.add_job(
                func=self.check_for_new_releases,
                trigger=IntervalTrigger(hours=interval_hours),
                args=[user_id],
                id=f'periodic_check_{user_id}',
                replace_existing=True
            )
            
            user_info = self.session_manager.load_session(user_id)
            user_name = user_info.get('user_name', user_id) if user_info else user_id
            
            print(f"📅 Scheduled periodic checks every {interval_hours} hours for user {user_name}")
            
        except Exception as e:
            print(f"❌ Error scheduling periodic checks for {user_id}: {e}")
    
    def remove_user_periodic_job(self, user_id: str):
        """Remove periodic job for a user"""
        try:
            job_id = f'periodic_check_{user_id}'
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
                print(f"🗑️ Removed periodic job for user {user_id}")
        except Exception as e:
            print(f"❌ Error removing periodic job for {user_id}: {e}")
    
    def check_for_new_releases(self, user_id: str):
        """Check for new releases for a specific user"""
        try:
            print(f"🔍 Checking for new releases for user {user_id}")
            
            # Get user session and settings
            session_data = self.session_manager.load_session(user_id)
            if not session_data:
                print(f"❌ No valid session for user {user_id}")
                return
            
            settings = self.session_manager.load_user_settings(user_id)
            user_name = session_data.get('user_name', user_id)
            
            # Get Spotify client
            sp = self.session_manager.get_valid_spotify_client(user_id, self.spotify_oauth)
            if not sp:
                print(f"❌ Could not get Spotify client for user {user_name}")
                return
            
            new_releases_found = False
            
            # Check artists if enabled
            if settings.get('download_artists', True):
                artist_releases = self.check_artist_releases(sp, user_id)
                if artist_releases:
                    new_releases_found = True
                    self.log_new_releases(user_id, 'artists', artist_releases)
                    
                    if settings.get('auto_download_enabled', False):
                        self.trigger_download(user_id, 'artists')
            
            # Check liked songs if enabled
            if settings.get('download_liked', True):
                liked_changes = self.check_liked_songs(sp, user_id)
                if liked_changes:
                    new_releases_found = True
                    self.log_new_releases(user_id, 'liked', liked_changes)
                    
                    if settings.get('auto_download_enabled', False):
                        self.trigger_download(user_id, 'liked')
            
            if new_releases_found:
                print(f"🎉 Found new releases for user {user_name}")
            else:
                print(f"📭 No new releases found for user {user_name}")
                
        except Exception as e:
            print(f"❌ Error checking releases for user {user_id}: {e}")
    
    def check_artist_releases(self, sp: spotipy.Spotify, user_id: str) -> List[Dict]:
        """Check for new artist releases"""
        try:
            # Load current artist data
            if not os.path.exists('artist_songs.json'):
                print("📭 No artist_songs.json found - run get_all_artist_data.py first")
                return []

            with open('artist_songs.json', 'r') as f:
                current_data = json.load(f)

            # Load last check timestamp
            last_check_file = f'logs/{user_id}_last_artist_check.json'
            last_check_time = None

            if os.path.exists(last_check_file):
                try:
                    with open(last_check_file, 'r') as f:
                        last_check_data = json.load(f)
                        last_check_time = last_check_data.get('timestamp')
                except:
                    pass

            new_releases = []
            current_time = datetime.now().isoformat()

            # For each artist, check if there are songs not in downloaded.json
            downloaded_file = 'downloaded.json'
            downloaded_songs = []
            if os.path.exists(downloaded_file):
                with open(downloaded_file, 'r') as f:
                    downloaded_songs = json.load(f)

            # Create a set of downloaded songs for quick lookup
            downloaded_set = set()
            for song in downloaded_songs:
                key = f"{song.get('artist', '')}|||{song.get('song_name', '')}"
                downloaded_set.add(key)

            for artist_name, artist_data in current_data.items():
                try:
                    print(f"🎵 Checking {artist_name} for new releases...")

                    # Check all songs from this artist
                    for category in ['albums', 'singles']:
                        for release_name, release_data in artist_data.get(category, {}).items():
                            for song in release_data.get('songs', []):
                                song_key = f"{artist_name}|||{song.get('name', '')}"

                                # If song is not downloaded, it's "new" for auto-download purposes
                                if song_key not in downloaded_set:
                                    new_releases.append({
                                        'artist': artist_name,
                                        'song_name': song.get('name', ''),
                                        'release_name': release_name,
                                        'release_type': category,
                                        'link': song.get('link', ''),
                                        'detected_at': current_time
                                    })

                except Exception as e:
                    print(f"❌ Error checking artist {artist_name}: {e}")
                    continue

            # Save current check timestamp
            try:
                with open(last_check_file, 'w') as f:
                    json.dump({
                        'timestamp': current_time,
                        'user_id': user_id,
                        'releases_found': len(new_releases)
                    }, f, indent=2)
            except Exception as e:
                print(f"⚠️ Could not save last check time: {e}")

            if new_releases:
                print(f"🎉 Found {len(new_releases)} undownloaded songs from artists!")
            else:
                print(f"📭 No new artist releases to download")

            return new_releases

        except Exception as e:
            print(f"❌ Error checking artist releases: {e}")
            return []
    
    def check_liked_songs(self, sp: spotipy.Spotify, user_id: str) -> List[Dict]:
        """Check for changes in liked songs"""
        try:
            print("💖 Fetching current liked songs from Spotify...")

            # Get current liked songs from Spotify
            results = sp.current_user_saved_tracks(limit=50)
            if not results:
                print("📭 No liked songs found on Spotify")
                return []

            current_liked = []
            while results:
                for item in results.get("items", []):
                    track = item.get("track")
                    if track:
                        current_liked.append({
                            "id": track.get("id"),
                            "name": track.get("name", ""),
                            "artist": track.get("artists", [{}])[0].get("name", ""),
                            "link": track.get("external_urls", {}).get("spotify", ""),
                            "added_at": item.get("added_at", "")
                        })

                if results.get("next"):
                    results = sp.next(results)
                else:
                    break

            print(f"📚 Found {len(current_liked)} total liked songs on Spotify")

            # Check against downloaded_liked.json instead of liked_songs.json
            downloaded_file = 'downloaded_liked.json'
            downloaded_songs = []
            if os.path.exists(downloaded_file):
                with open(downloaded_file, 'r') as f:
                    downloaded_songs = json.load(f)

            # Create a set of downloaded songs for quick lookup
            downloaded_set = set()
            for song in downloaded_songs:
                # Use artist + song name as key since Spotify IDs might not be stored
                key = f"{song.get('artist', '')}|||{song.get('song_name', '')}"
                downloaded_set.add(key)

            # Find songs that are liked but not downloaded
            new_songs = []
            for song in current_liked:
                song_key = f"{song.get('artist', '')}|||{song.get('name', '')}"
                if song_key not in downloaded_set:
                    new_songs.append({
                        'id': song.get('id'),
                        'name': song.get('name'),
                        'artist': song.get('artist'),
                        'link': song.get('link'),
                        'added_at': song.get('added_at'),
                        'detected_at': datetime.now().isoformat()
                    })

            # Update the liked_songs.json file for future reference
            liked_file = 'liked_songs.json'
            try:
                with open(liked_file, 'w') as f:
                    json.dump(current_liked, f, indent=2)
                print(f"📝 Updated {liked_file} with current liked songs")
            except Exception as e:
                print(f"⚠️ Could not update {liked_file}: {e}")

            if new_songs:
                print(f"🎉 Found {len(new_songs)} new liked songs to download!")
                for song in new_songs[:5]:  # Show first 5
                    print(f"   🎵 {song['artist']} - {song['name']}")
                if len(new_songs) > 5:
                    print(f"   ... and {len(new_songs) - 5} more")
            else:
                print("📭 No new liked songs to download")

            return new_songs

        except Exception as e:
            print(f"❌ Error checking liked songs: {e}")
            return []
    
    def log_new_releases(self, user_id: str, release_type: str, releases: List[Dict]):
        """Log new releases to file"""
        try:
            log_file = f'logs/{user_id}_new_releases.json'
            
            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'user_id': user_id,
                'type': release_type,
                'count': len(releases),
                'releases': releases
            }
            
            # Load existing logs
            logs = []
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    logs = json.load(f)
            
            logs.append(log_entry)
            
            # Keep only last 100 entries
            logs = logs[-100:]
            
            with open(log_file, 'w') as f:
                json.dump(logs, f, indent=2)
            
            print(f"📝 Logged {len(releases)} new {release_type} releases for user {user_id}")
            
        except Exception as e:
            print(f"❌ Error logging new releases: {e}")
    
    def trigger_download(self, user_id: str, download_type: str):
        """Trigger automatic download for a user"""
        try:
            print(f"⬇️ Triggering automatic download of {download_type} for user {user_id}")

            # Import here to avoid circular imports
            import subprocess
            import threading

            # Create a background thread to handle the download
            download_thread = threading.Thread(
                target=self._run_background_download,
                args=(user_id, download_type),
                daemon=True
            )
            download_thread.start()

            print(f"🚀 Background download started for {download_type}")

        except Exception as e:
            print(f"❌ Error triggering download: {e}")

    def _run_background_download(self, user_id: str, download_type: str):
        """Run download in background thread"""
        try:
            session_data = self.session_manager.load_session(user_id)
            if not session_data:
                print(f"❌ No session data for user {user_id}")
                return

            user_name = session_data.get('user_name', user_id)
            print(f"🎵 Starting background download for {user_name} ({download_type})")

            if download_type == 'artists':
                self._download_artists_background(user_id)
            elif download_type == 'liked':
                self._download_liked_background(user_id)
            else:
                print(f"❌ Unknown download type: {download_type}")

        except Exception as e:
            print(f"❌ Error in background download: {e}")

    def _download_artists_background(self, user_id: str):
        """Download artist songs in background"""
        try:
            print(f"🎸 Running artist download for user {user_id}")

            # Load artist_songs.json
            if not os.path.exists('artist_songs.json'):
                error_msg = "artist_songs.json not found"
                print(f"❌ {error_msg}")
                self._log_download_completion(user_id, 'artists', False, error_msg)
                return

            with open('artist_songs.json', 'r') as f:
                data = json.load(f)

            # Load or create downloaded.json
            downloaded_json_path = 'downloaded.json'
            if os.path.exists(downloaded_json_path):
                with open(downloaded_json_path, 'r') as f:
                    downloaded_songs = json.load(f)
            else:
                downloaded_songs = []

            # Collect all songs
            all_songs = []
            for artist, artist_data in data.items():
                for category in ['albums', 'singles']:
                    for release_name, release_data in artist_data.get(category, {}).items():
                        for song in release_data.get('songs', []):
                            all_songs.append({
                                'artist': artist,
                                'song_name': song['name'],
                                'link': song['link']
                            })

            # Find missing songs
            missing_songs = self._find_missing_songs(all_songs, downloaded_songs, data.keys())

            if not missing_songs:
                print(f"📭 No new artist songs to download for user {user_id}")
                self._log_download_completion(user_id, 'artists', True, "No new songs to download")
                return

            # Download missing songs
            success_count = 0
            total_count = len(missing_songs)

            for i, song in enumerate(missing_songs):
                try:
                    success = self._download_single_song(song, downloaded_songs, downloaded_json_path)
                    if success:
                        success_count += 1
                    print(f"Progress: {i+1}/{total_count} songs processed")
                except Exception as e:
                    print(f"Error downloading {song['artist']} - {song['song_name']}: {e}")

            result_msg = f"Downloaded {success_count}/{total_count} songs successfully"
            print(f"✅ Artist download completed for user {user_id}: {result_msg}")
            self._log_download_completion(user_id, 'artists', True, result_msg)

        except Exception as e:
            print(f"❌ Error in artist download for user {user_id}: {e}")
            self._log_download_completion(user_id, 'artists', False, str(e))

    def _download_liked_background(self, user_id: str):
        """Download liked songs in background"""
        try:
            print(f"💖 Running liked songs download for user {user_id}")

            # Load liked_songs.json
            if not os.path.exists('liked_songs.json'):
                error_msg = "liked_songs.json not found - run get_user_fav_songs.py first"
                print(f"❌ {error_msg}")
                self._log_download_completion(user_id, 'liked', False, error_msg)
                return

            with open('liked_songs.json', 'r') as f:
                liked_songs_data = json.load(f)

            # Load or create downloaded_liked.json
            downloaded_json_path = 'downloaded_liked.json'
            if os.path.exists(downloaded_json_path):
                with open(downloaded_json_path, 'r') as f:
                    downloaded_songs = json.load(f)
            else:
                downloaded_songs = []

            # Convert liked songs to the format expected by download functions
            all_songs = []
            for song in liked_songs_data:
                all_songs.append({
                    'artist': song.get('artist', 'Unknown Artist'),
                    'song_name': song.get('name', ''),
                    'link': song.get('link', '')
                })

            # Get unique artists for directory creation
            unique_artists = set(song['artist'] for song in all_songs)

            # Find missing songs
            missing_songs = self._find_missing_songs(all_songs, downloaded_songs, unique_artists)

            if not missing_songs:
                print(f"📭 No new liked songs to download for user {user_id}")
                self._log_download_completion(user_id, 'liked', True, "No new songs to download")
                return

            # Download missing songs
            success_count = 0
            total_count = len(missing_songs)

            for i, song in enumerate(missing_songs):
                try:
                    success = self._download_single_song(song, downloaded_songs, downloaded_json_path)
                    if success:
                        success_count += 1
                    print(f"Progress: {i+1}/{total_count} songs processed")
                except Exception as e:
                    print(f"Error downloading {song['artist']} - {song['song_name']}: {e}")

            result_msg = f"Downloaded {success_count}/{total_count} songs successfully"
            print(f"✅ Liked songs download completed for user {user_id}: {result_msg}")
            self._log_download_completion(user_id, 'liked', True, result_msg)

        except Exception as e:
            print(f"❌ Error in liked songs download for user {user_id}: {e}")
            self._log_download_completion(user_id, 'liked', False, str(e))

    def _log_download_completion(self, user_id: str, download_type: str, success: bool, details: str):
        """Log download completion details"""
        try:
            log_file = f'logs/{user_id}_auto_downloads.json'

            log_entry = {
                'timestamp': datetime.now().isoformat(),
                'user_id': user_id,
                'download_type': download_type,
                'success': success,
                'details': details[:1000] if details else ""  # Limit details length
            }

            # Load existing logs
            logs = []
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    logs = json.load(f)

            logs.append(log_entry)

            # Keep only last 50 entries
            logs = logs[-50:]

            with open(log_file, 'w') as f:
                json.dump(logs, f, indent=2)

            status = "✅ Success" if success else "❌ Failed"
            print(f"📝 Logged {download_type} download completion for user {user_id}: {status}")

        except Exception as e:
            print(f"❌ Error logging download completion: {e}")

    def _find_missing_songs(self, all_songs: List[Dict], downloaded_songs: List[Dict], artists: set) -> List[Dict]:
        """Find songs that need to be downloaded - checks both artist and liked download tracking"""
        try:
            import re

            def normalize_filename(name):
                """Remove or replace invalid characters for filenames"""
                name = re.sub(r'[<>:"/\\|?*]', '', name)
                return name

            # Load BOTH download tracking files to avoid duplicates
            all_downloaded_songs = list(downloaded_songs)  # Start with the provided list

            # Load the other tracking file
            other_json_files = ['downloaded.json', 'downloaded_liked.json']
            for json_file in other_json_files:
                if os.path.exists(json_file):
                    try:
                        with open(json_file, 'r') as f:
                            other_downloaded = json.load(f)
                            # Add songs from other file if not already in our list
                            for other_song in other_downloaded:
                                # Check if this song is already in our list
                                already_exists = any(
                                    ds.get('artist') == other_song.get('artist') and
                                    ds.get('song_name') == other_song.get('song_name')
                                    for ds in all_downloaded_songs
                                )
                                if not already_exists:
                                    all_downloaded_songs.append(other_song)
                    except Exception as e:
                        print(f"⚠️ Could not load {json_file}: {e}")

            print(f"📊 Checking against {len(all_downloaded_songs)} total downloaded songs from all sources")

            # Check what's already downloaded in folders
            music_dir = 'music'
            downloaded_files = {}

            for artist in artists:
                # Use normalized artist name for directory lookup
                normalized_artist = normalize_filename(artist)
                artist_dir = os.path.join(music_dir, normalized_artist)
                if os.path.exists(artist_dir):
                    downloaded_files[artist] = [normalize_filename(f).lower() for f in os.listdir(artist_dir)]
                else:
                    downloaded_files[artist] = []

            # Find missing songs
            missing_songs = []
            for song in all_songs:
                artist = song['artist']
                expected_filename = f"{song['artist']} - {song['song_name']}.mp3"
                normalized_expected = normalize_filename(expected_filename).lower()

                # Check if in folder
                in_folder = normalized_expected in downloaded_files.get(artist, [])

                # Check if in ANY downloaded tracking file
                in_downloaded = any(
                    ds.get('artist') == song['artist'] and ds.get('song_name') == song['song_name']
                    for ds in all_downloaded_songs
                )

                if not in_folder and not in_downloaded:
                    missing_songs.append(song)
                elif in_downloaded and not in_folder:
                    print(f"⏭️ Skipping {song['artist']} - {song['song_name']}: Already downloaded in other category! 📋")

            return missing_songs

        except Exception as e:
            print(f"❌ Error finding missing songs: {e}")
            return []

    def _download_single_song(self, song: Dict, downloaded_songs: List[Dict], downloaded_json_path: str) -> bool:
        """Download a single song and update tracking"""
        try:
            import subprocess
            import re

            def normalize_filename(name):
                """Remove or replace invalid characters for filenames"""
                name = re.sub(r'[<>:"/\\|?*]', '', name)
                return name

            # Normalize artist name for directory creation
            normalized_artist = normalize_filename(song['artist'])
            # Set a music directory for docker compatibility
            music_dir = 
            artist_dir = os.path.join(music_dir, normalized_artist)

            if not os.path.exists(artist_dir):
                os.makedirs(artist_dir)
                print(f"📁 Created folder for {song['artist']}")

            print(f"🎵 Downloading: {song['artist']} - {song['song_name']}")

            # Download using spotdl
            result = subprocess.run(
                ['spotdl', song['link'], '--output', artist_dir],
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout per song
            )

            if result.returncode == 0:
                print(f"✅ Downloaded successfully: {song['artist']} - {song['song_name']}")
                print(f"📂 Saved to: {artist_dir}")

                # Add to downloaded_songs
                downloaded_songs.append({
                    'artist': song['artist'],
                    'song_name': song['song_name'],
                    'link': song['link'],
                    'downloaded_at': datetime.now().isoformat()
                })

                # Save downloaded.json
                with open(downloaded_json_path, 'w') as f:
                    json.dump(downloaded_songs, f, indent=4)

                return True
            else:
                print(f"❌ Failed to download: {song['artist']} - {song['song_name']}")
                print(f"Error: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            print(f"⏰ Download timed out: {song['artist']} - {song['song_name']}")
            return False
        except Exception as e:
            print(f"❌ Error downloading {song['artist']} - {song['song_name']}: {e}")
            return False
    
    def get_scheduled_jobs(self) -> List[Dict]:
        """Get list of currently scheduled jobs"""
        try:
            jobs = []
            for job in self.scheduler.get_jobs():
                jobs.append({
                    'id': job.id,
                    'name': job.name,
                    'next_run': job.next_run_time.isoformat() if job.next_run_time else None,
                    'trigger': str(job.trigger)
                })
            return jobs
        except Exception as e:
            print(f"❌ Error getting scheduled jobs: {e}")
            return []
