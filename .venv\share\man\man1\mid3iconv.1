.\" Man page generated from reStructuredText.
.
.TH MID3ICONV 1 "" "" ""
.SH NAME
mid3iconv \- convert ID3 tag encodings
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.SH SYNOPSIS
.sp
\fBmid3iconv\fP [\fIoptions\fP] \fIfilename\fP ...
.SH DESCRIPTION
.sp
\fBmid3iconv\fP converts ID3 tags from legacy encodings to Unicode and stores
them using the ID3v2 format.
.SH OPTIONS
.INDENT 0.0
.TP
.B \-\-debug\fP,\fB  \-d
Print updated tags
.TP
.B \-\-dry\-run\fP,\fB  \-p
Do not actually modify files
.TP
.B \-\-encoding\fP,\fB  \-e
Convert from this encoding. By default, your locale\(aqs default encoding is
used.
.TP
.B \-\-force\-v1
Use an ID3v1 tag even if an ID3v2 tag is present
.TP
.B \-\-quiet\fP,\fB  \-q
Only output errors
.TP
.B \-\-remove\-v1
Remove any ID3v1 tag after processing the files
.UNINDENT
.SH AUTHOR
.sp
Emfox Zhou.
.sp
Based on id3iconv (\fI\%http://www.cs.berkeley.edu/~zf/id3iconv/\fP) by Feng Zhou.
.\" Generated by docutils manpage writer.
.
